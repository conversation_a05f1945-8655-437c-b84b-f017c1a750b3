/**
 * Workflow Templates API
 * 
 * Provides access to workflow templates with agent consultation capabilities
 */

import { NextRequest, NextResponse } from 'next/server';
import { getEnhancedTemplateRegistry } from '../../../../core/workflow/singleton';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const agentEnabled = searchParams.get('agentEnabled');

    const registry = getEnhancedTemplateRegistry();
    let templates = registry.getAllTemplates();

    // Apply filters
    if (category && category !== 'all') {
      templates = templates.filter(t => t.workflow.metadata.category === category);
    }

    if (featured === 'true') {
      templates = templates.filter(t => t.featured);
    }

    if (agentEnabled === 'true') {
      templates = templates.filter(t => 
        t.workflow.steps.some(step => step.consultationConfig?.enabled)
      );
    }

    // Transform templates for frontend consumption
    const transformedTemplates = templates.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      instructions: template.instructions || template.description,
      featured: template.featured || false,
      sampleInputs: template.sampleInputs || {},
      workflow: {
        metadata: {
          category: template.workflow.metadata.category,
          difficulty: template.workflow.metadata.difficulty,
          estimatedTime: template.workflow.metadata.estimatedTime,
          tags: template.workflow.metadata.tags || []
        },
        steps: template.workflow.steps.map(step => ({
          id: step.id,
          name: step.name,
          type: step.type,
          dependencies: step.dependencies || [],
          consultationConfig: step.consultationConfig
        })),
        agentConsultationEnabled: template.workflow.steps.some(step => 
          step.consultationConfig?.enabled
        )
      },
      // Additional metadata for UI
      consultationEnabled: template.workflow.steps.some(step => 
        step.consultationConfig?.enabled
      ),
      agentCount: template.workflow.steps.filter(step => 
        step.consultationConfig?.enabled
      ).reduce((count, step) => {
        const agents = step.consultationConfig?.triggers?.[0]?.agents || [];
        return Math.max(count, agents.length);
      }, 0),
      stepCount: template.workflow.steps.length
    }));

    return NextResponse.json({
      success: true,
      data: {
        templates: transformedTemplates,
        total: transformedTemplates.length,
        categories: Array.from(new Set(templates.map(t => t.workflow.metadata.category))),
        featuredCount: transformedTemplates.filter(t => t.featured).length,
        agentEnabledCount: transformedTemplates.filter(t => t.consultationEnabled).length
      }
    });

  } catch (error) {
    console.error('❌ Failed to get workflow templates:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get workflow templates'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, inputs, userId } = body;

    if (!templateId) {
      return NextResponse.json({
        success: false,
        error: 'Template ID is required'
      }, { status: 400 });
    }

    const registry = getEnhancedTemplateRegistry();
    
    // Get template
    const template = registry.getTemplate(templateId);
    if (!template) {
      return NextResponse.json({
        success: false,
        error: 'Template not found'
      }, { status: 404 });
    }

    // Process template for execution
    const processed = registry.processTemplate(templateId, userId);
    if (!processed) {
      return NextResponse.json({
        success: false,
        error: 'Failed to process template'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        template: {
          id: template.id,
          name: template.name,
          description: template.description
        },
        workflow: processed.workflow,
        approvalGates: processed.approvalGates,
        processedSteps: processed.workflow.steps.length,
        approvalGatesCount: processed.approvalGates.length
      }
    });

  } catch (error) {
    console.error('❌ Failed to process workflow template:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process workflow template'
    }, { status: 500 });
  }
}

// Get specific template details
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId } = body;

    if (!templateId) {
      return NextResponse.json({
        success: false,
        error: 'Template ID is required'
      }, { status: 400 });
    }

    const registry = getEnhancedTemplateRegistry();
    const template = registry.getTemplate(templateId);
    
    if (!template) {
      return NextResponse.json({
        success: false,
        error: 'Template not found'
      }, { status: 404 });
    }

    // Return detailed template information
    const detailedTemplate = {
      id: template.id,
      name: template.name,
      description: template.description,
      instructions: template.instructions || template.description,
      featured: template.featured || false,
      sampleInputs: template.sampleInputs || {},
      workflow: {
        name: template.workflow.name,
        description: template.workflow.description,
        version: template.workflow.version,
        metadata: template.workflow.metadata,
        steps: template.workflow.steps.map(step => ({
          id: step.id,
          name: step.name,
          type: step.type,
          config: step.config,
          inputs: step.inputs,
          outputs: step.outputs,
          dependencies: step.dependencies || [],
          consultationConfig: step.consultationConfig
        }))
      },
      // Analysis
      analysis: {
        totalSteps: template.workflow.steps.length,
        agentEnabledSteps: template.workflow.steps.filter(step => 
          step.consultationConfig?.enabled
        ).length,
        humanReviewSteps: template.workflow.steps.filter(step => 
          step.type === 'HUMAN_REVIEW'
        ).length,
        approvalGates: template.workflow.steps.filter(step => 
          step.type === 'APPROVAL_GATE'
        ).length,
        estimatedDuration: template.workflow.metadata.estimatedTime || 30,
        complexity: template.workflow.metadata.difficulty || 'easy',
        agentTypes: Array.from(new Set(
          template.workflow.steps
            .filter(step => step.consultationConfig?.enabled)
            .flatMap(step => 
              step.consultationConfig?.triggers?.[0]?.agents || []
            )
        ))
      }
    };

    return NextResponse.json({
      success: true,
      data: {
        template: detailedTemplate
      }
    });

  } catch (error) {
    console.error('❌ Failed to get template details:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get template details'
    }, { status: 500 });
  }
}

// Template validation endpoint (moved from /template route)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { template } = body;

    if (!template) {
      return NextResponse.json({
        success: false,
        error: 'Template is required'
      }, { status: 400 });
    }

    const { TemplateProcessor } = await import('@/core/workflow/template-processor');
    const validation = TemplateProcessor.validateTemplate(template);

    return NextResponse.json({
      success: true,
      data: {
        validation,
        executionOrder: validation.valid ? TemplateProcessor.getExecutionOrder(template) : null
      }
    });

  } catch (error) {
    console.error('❌ Template validation error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to validate template'
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed'
  }, { status: 405 });
}
