'use client';

import React, { useState, useRef } from 'react';
import { 
  DocumentArrowUpIcon, 
  DocumentArrowDownIcon, 
  PlayIcon, 
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';

interface BulkJob {
  id: string;
  name: string;
  templateId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalItems: number;
  processedItems: number;
  createdAt: string;
  completedAt?: string;
  results?: Array<{
    id: string;
    status: 'success' | 'failed';
    executionId?: string;
    error?: string;
  }>;
}

interface BulkOperationsInterfaceProps {
  className?: string;
}

export default function BulkOperationsInterface({ className = '' }: BulkOperationsInterfaceProps) {
  const [activeTab, setActiveTab] = useState<'import' | 'jobs' | 'export'>('import');
  const [csvData, setCsvData] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState('seo-blog-post');
  const [bulkJobs, setBulkJobs] = useState<BulkJob[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const templates = [
    { id: 'seo-blog-post', name: 'SEO Blog Post', fields: ['topic', 'target_audience', 'primary_keyword'] },
    { id: 'product-descriptions', name: 'Product Descriptions', fields: ['product_name', 'brand_voice', 'product_category'] },
    { id: 'social-media', name: 'Social Media Posts', fields: ['platform', 'content_type', 'target_audience'] }
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const lines = text.split('\n');
      const headers = lines[0].split(',').map(h => h.trim());
      
      const data = lines.slice(1)
        .filter(line => line.trim())
        .map((line, index) => {
          const values = line.split(',').map(v => v.trim());
          const row: any = { id: index + 1 };
          headers.forEach((header, i) => {
            row[header] = values[i] || '';
          });
          return row;
        });
      
      setCsvData(data);
    };
    reader.readAsText(file);
  };

  const startBulkProcessing = async () => {
    if (csvData.length === 0) {
      alert('Please upload a CSV file first');
      return;
    }

    setIsProcessing(true);
    
    try {
      const response = await fetch('/api/workflow/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate,
          data: csvData,
          options: {
            batchSize: 5,
            delayBetweenBatches: 1000
          }
        })
      });

      const result = await response.json();
      
      if (result.success) {
        const newJob: BulkJob = {
          id: result.data.jobId || Date.now().toString(),
          name: `Bulk ${templates.find(t => t.id === selectedTemplate)?.name} - ${csvData.length} items`,
          templateId: selectedTemplate,
          status: 'running',
          progress: 0,
          totalItems: csvData.length,
          processedItems: 0,
          createdAt: new Date().toISOString()
        };
        
        setBulkJobs(prev => [newJob, ...prev]);
        setActiveTab('jobs');
        
        // Simulate progress updates
        simulateJobProgress(newJob.id);
      } else {
        alert('Failed to start bulk processing: ' + result.error);
      }
    } catch (error) {
      console.error('Bulk processing error:', error);
      alert('Failed to start bulk processing');
    } finally {
      setIsProcessing(false);
    }
  };

  const simulateJobProgress = (jobId: string) => {
    const interval = setInterval(() => {
      setBulkJobs(prev => prev.map(job => {
        if (job.id === jobId && job.status === 'running') {
          const newProgress = Math.min(job.progress + Math.random() * 20, 100);
          const newProcessedItems = Math.floor((newProgress / 100) * job.totalItems);
          
          if (newProgress >= 100) {
            clearInterval(interval);
            return {
              ...job,
              status: 'completed' as const,
              progress: 100,
              processedItems: job.totalItems,
              completedAt: new Date().toISOString(),
              results: Array.from({ length: job.totalItems }, (_, i) => ({
                id: (i + 1).toString(),
                status: Math.random() > 0.1 ? 'success' as const : 'failed' as const,
                executionId: Math.random() > 0.1 ? `exec-${Date.now()}-${i}` : undefined,
                error: Math.random() > 0.1 ? undefined : 'Sample error message'
              }))
            };
          }
          
          return {
            ...job,
            progress: newProgress,
            processedItems: newProcessedItems
          };
        }
        return job;
      }));
    }, 2000);
  };

  const exportResults = (job: BulkJob) => {
    if (!job.results) return;
    
    const csvContent = [
      'ID,Status,Execution ID,Error',
      ...job.results.map(result => 
        `${result.id},${result.status},${result.executionId || ''},${result.error || ''}`
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bulk-results-${job.id}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: BulkJob['status']) => {
    switch (status) {
      case 'pending': return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'running': return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600" />;
      case 'completed': return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'failed': return <XCircleIcon className="h-5 w-5 text-red-600" />;
    }
  };

  const getStatusColor = (status: BulkJob['status']) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">Bulk Operations</h2>
        <p className="text-sm text-gray-600 mt-1">
          Process multiple workflows in batch using CSV import/export
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'import', name: 'Import & Process', icon: DocumentArrowUpIcon },
            { id: 'jobs', name: 'Bulk Jobs', icon: PlayIcon },
            { id: 'export', name: 'Export Results', icon: DocumentArrowDownIcon }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'import' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">CSV Import & Processing</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Template
                  </label>
                  <select
                    value={selectedTemplate}
                    onChange={(e) => setSelectedTemplate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                  <p className="text-sm text-gray-600 mt-1">
                    Required fields: {templates.find(t => t.id === selectedTemplate)?.fields.join(', ')}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Upload CSV File
                  </label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={handleFileUpload}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {csvData.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Preview ({csvData.length} rows)</h4>
                    <div className="overflow-x-auto border border-gray-200 rounded-lg">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            {Object.keys(csvData[0] || {}).map(key => (
                              <th key={key} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                {key}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {csvData.slice(0, 5).map((row, index) => (
                            <tr key={index}>
                              {Object.values(row).map((value: any, i) => (
                                <td key={i} className="px-4 py-2 text-sm text-gray-900">
                                  {String(value)}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {csvData.length > 5 && (
                        <div className="px-4 py-2 text-sm text-gray-500 bg-gray-50">
                          ... and {csvData.length - 5} more rows
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <button
                  onClick={startBulkProcessing}
                  disabled={isProcessing || csvData.length === 0}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Starting Bulk Processing...</span>
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4" />
                      <span>Start Bulk Processing</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'jobs' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Bulk Processing Jobs</h3>
            
            {bulkJobs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <PlayIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No bulk jobs yet</p>
                <p className="text-sm mt-1">Start a bulk processing job from the Import tab</p>
              </div>
            ) : (
              <div className="space-y-4">
                {bulkJobs.map(job => (
                  <div key={job.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(job.status)}
                        <div>
                          <h4 className="font-medium text-gray-900">{job.name}</h4>
                          <p className="text-sm text-gray-600">
                            {job.processedItems} / {job.totalItems} items processed
                          </p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                        {job.status.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${job.progress}%` }}
                      ></div>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Started: {new Date(job.createdAt).toLocaleString()}</span>
                      {job.completedAt && (
                        <span>Completed: {new Date(job.completedAt).toLocaleString()}</span>
                      )}
                    </div>
                    
                    {job.status === 'completed' && job.results && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-gray-600">
                            Success: {job.results.filter(r => r.status === 'success').length} | 
                            Failed: {job.results.filter(r => r.status === 'failed').length}
                          </div>
                          <button
                            onClick={() => exportResults(job)}
                            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                          >
                            Export Results
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'export' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Export Results</h3>

            {bulkJobs.filter(job => job.status === 'completed').length > 0 ? (
              <div className="space-y-4">
                <p className="text-gray-600">Export results from completed bulk jobs:</p>
                {bulkJobs
                  .filter(job => job.status === 'completed')
                  .map(job => (
                    <div key={job.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">{job.name}</h4>
                        <p className="text-sm text-gray-600">
                          Completed: {new Date(job.completedAt!).toLocaleString()}
                        </p>
                      </div>
                      <button
                        onClick={() => exportResults(job)}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-2"
                      >
                        <DocumentArrowDownIcon className="h-4 w-4" />
                        <span>Export CSV</span>
                      </button>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DocumentArrowDownIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No completed bulk jobs to export</p>
                <p className="text-sm mt-1">Complete some bulk processing jobs first</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
