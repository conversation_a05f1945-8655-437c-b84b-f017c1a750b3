'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { CheckCircleIcon, ExclamationTriangleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

interface ExecutionOrder {
  steps: Array<{
    id: string;
    name: string;
    level: number;
    dependencies: string[];
  }>;
  levels: number;
  parallelizable: boolean;
}

interface TemplateValidationPanelProps {
  template: any;
  onValidationComplete?: (isValid: boolean, result: ValidationResult) => void;
  className?: string;
}

export default function TemplateValidationPanel({ 
  template, 
  onValidationComplete, 
  className = '' 
}: TemplateValidationPanelProps) {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [executionOrder, setExecutionOrder] = useState<ExecutionOrder | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const validateTemplate = useCallback(async () => {
    if (!template) return;

    setIsValidating(true);
    try {
      const response = await fetch('/api/workflow/templates', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ template })
      });

      const result = await response.json();

      if (result.success) {
        const validation = result.data.validation;
        const order = result.data.executionOrder;

        setValidationResult(validation);
        setExecutionOrder(order);

        if (onValidationComplete) {
          onValidationComplete(validation.valid, validation);
        }
      } else {
        setValidationResult({
          valid: false,
          errors: [result.error || 'Validation failed'],
          warnings: []
        });
      }
    } catch (error) {
      console.error('Template validation error:', error);
      setValidationResult({
        valid: false,
        errors: ['Failed to validate template'],
        warnings: []
      });
    } finally {
      setIsValidating(false);
    }
  }, [template, onValidationComplete]);

  useEffect(() => {
    if (template) {
      validateTemplate();
    }
  }, [template, validateTemplate]);

  const getValidationIcon = () => {
    if (isValidating) {
      return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600" />;
    }
    
    if (!validationResult) return null;
    
    if (validationResult.valid) {
      return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
    } else {
      return <XCircleIcon className="h-5 w-5 text-red-600" />;
    }
  };

  const getValidationStatus = () => {
    if (isValidating) return 'Validating...';
    if (!validationResult) return 'Not validated';
    if (validationResult.valid) return 'Valid';
    return 'Invalid';
  };

  const getStatusColor = () => {
    if (isValidating) return 'text-blue-600';
    if (!validationResult) return 'text-gray-500';
    if (validationResult.valid) return 'text-green-600';
    return 'text-red-600';
  };

  if (!template) {
    return (
      <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
        <p className="text-gray-500 text-sm">Select a template to view validation</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getValidationIcon()}
            <h3 className="text-lg font-semibold text-gray-900">Template Validation</h3>
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              {getValidationStatus()}
            </span>
          </div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>
      </div>

      <div className="p-4">
        {/* Quick Status */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-sm text-gray-600">Template</div>
            <div className="font-semibold text-gray-900">{template.name || 'Unnamed'}</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="text-sm text-gray-600">Steps</div>
            <div className="font-semibold text-gray-900">
              {template.workflow?.steps?.length || 0} steps
            </div>
          </div>
        </div>

        {/* Validation Results */}
        {validationResult && (
          <div className="space-y-3">
            {/* Errors */}
            {validationResult.errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <XCircleIcon className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">
                    {validationResult.errors.length} Error{validationResult.errors.length !== 1 ? 's' : ''}
                  </span>
                </div>
                <ul className="text-sm text-red-700 space-y-1">
                  {validationResult.errors.map((error, index) => (
                    <li key={index} className="flex items-start space-x-1">
                      <span>•</span>
                      <span>{error}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Warnings */}
            {validationResult.warnings.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <ExclamationTriangleIcon className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    {validationResult.warnings.length} Warning{validationResult.warnings.length !== 1 ? 's' : ''}
                  </span>
                </div>
                <ul className="text-sm text-yellow-700 space-y-1">
                  {validationResult.warnings.map((warning, index) => (
                    <li key={index} className="flex items-start space-x-1">
                      <span>•</span>
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Success */}
            {validationResult.valid && validationResult.errors.length === 0 && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Template is valid and ready for execution
                  </span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Execution Order Details */}
        {showDetails && executionOrder && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center space-x-2">
              <ClockIcon className="h-4 w-4" />
              <span>Execution Order</span>
            </h4>
            
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4 text-xs">
                <div className="bg-blue-50 rounded p-2">
                  <div className="text-blue-600 font-medium">Execution Levels</div>
                  <div className="text-blue-900 font-semibold">{executionOrder.levels}</div>
                </div>
                <div className="bg-purple-50 rounded p-2">
                  <div className="text-purple-600 font-medium">Parallelizable</div>
                  <div className="text-purple-900 font-semibold">
                    {executionOrder.parallelizable ? 'Yes' : 'No'}
                  </div>
                </div>
                <div className="bg-gray-50 rounded p-2">
                  <div className="text-gray-600 font-medium">Total Steps</div>
                  <div className="text-gray-900 font-semibold">{executionOrder.steps.length}</div>
                </div>
              </div>

              <div className="space-y-2">
                {Array.from({ length: executionOrder.levels }, (_, level) => {
                  const levelSteps = executionOrder.steps.filter(step => step.level === level);
                  return (
                    <div key={level} className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-semibold">
                        {level + 1}
                      </div>
                      <div className="flex-1 bg-gray-50 rounded-lg p-2">
                        <div className="flex flex-wrap gap-1">
                          {levelSteps.map(step => (
                            <span
                              key={step.id}
                              className="inline-block bg-white border border-gray-200 rounded px-2 py-1 text-xs text-gray-700"
                              title={`Dependencies: ${step.dependencies.join(', ') || 'None'}`}
                            >
                              {step.name}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between">
          <button
            onClick={validateTemplate}
            disabled={isValidating}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isValidating ? 'Validating...' : 'Re-validate'}
          </button>
          
          {validationResult?.valid && (
            <span className="text-xs text-green-600 flex items-center space-x-1">
              <CheckCircleIcon className="h-3 w-3" />
              <span>Ready to execute</span>
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
