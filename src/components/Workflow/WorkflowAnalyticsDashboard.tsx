'use client';

import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon, 
  ClockIcon, 
  TrendingUpIcon, 
  UserGroupIcon,
  CheckCircleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';

interface WorkflowAnalytics {
  overview: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    totalProcessingTime: number;
  };
  templateUsage: Array<{
    templateId: string;
    templateName: string;
    usageCount: number;
    successRate: number;
    averageTime: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    executions: number;
    successes: number;
    failures: number;
  }>;
  agentMetrics: {
    totalConsultations: number;
    averageConfidence: number;
    consultationsByAgent: Record<string, number>;
    consultationsByTrigger: Record<string, number>;
  };
  performanceMetrics: {
    peakHours: Array<{ hour: number; count: number }>;
    averageStepsPerWorkflow: number;
    mostCommonFailureReasons: Array<{ reason: string; count: number }>;
  };
}

interface WorkflowAnalyticsDashboardProps {
  className?: string;
  timeRange?: '24h' | '7d' | '30d' | '90d';
}

export default function WorkflowAnalyticsDashboard({ 
  className = '', 
  timeRange = '7d' 
}: WorkflowAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<WorkflowAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  useEffect(() => {
    fetchAnalytics();
  }, [selectedTimeRange]);

  const fetchAnalytics = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/workflow/analytics?timeRange=${selectedTimeRange}`);
      const result = await response.json();
      
      if (result.success) {
        setAnalytics(result.data);
      } else {
        // Fallback to mock data for demonstration
        setAnalytics(generateMockAnalytics());
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      // Fallback to mock data
      setAnalytics(generateMockAnalytics());
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockAnalytics = (): WorkflowAnalytics => {
    return {
      overview: {
        totalExecutions: 156,
        successfulExecutions: 142,
        failedExecutions: 14,
        averageExecutionTime: 45.2,
        totalProcessingTime: 7051.2
      },
      templateUsage: [
        { templateId: 'seo-blog-post', templateName: 'SEO Blog Post', usageCount: 89, successRate: 0.94, averageTime: 42.1 },
        { templateId: 'product-descriptions', templateName: 'Product Descriptions', usageCount: 45, successRate: 0.89, averageTime: 38.5 },
        { templateId: 'social-media', templateName: 'Social Media Posts', usageCount: 22, successRate: 0.95, averageTime: 28.3 }
      ],
      timeSeriesData: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        executions: Math.floor(Math.random() * 30) + 10,
        successes: Math.floor(Math.random() * 25) + 8,
        failures: Math.floor(Math.random() * 5) + 1
      })),
      agentMetrics: {
        totalConsultations: 234,
        averageConfidence: 0.87,
        consultationsByAgent: {
          'seo-keyword': 98,
          'content-strategy': 87,
          'market-research': 49
        },
        consultationsByTrigger: {
          'always': 145,
          'quality_threshold': 56,
          'feedback_keywords': 23,
          'content_complexity': 10
        }
      },
      performanceMetrics: {
        peakHours: Array.from({ length: 24 }, (_, hour) => ({
          hour,
          count: Math.floor(Math.random() * 20) + (hour >= 9 && hour <= 17 ? 10 : 2)
        })),
        averageStepsPerWorkflow: 4.2,
        mostCommonFailureReasons: [
          { reason: 'API timeout', count: 8 },
          { reason: 'Invalid input', count: 4 },
          { reason: 'Agent unavailable', count: 2 }
        ]
      }
    };
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getSuccessRate = () => {
    if (!analytics) return 0;
    return Math.round((analytics.overview.successfulExecutions / analytics.overview.totalExecutions) * 100);
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <ChartBarIcon className="h-8 w-8 mx-auto mb-2" />
          <p>No analytics data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <ChartBarIcon className="h-6 w-6 mr-2" />
            Workflow Analytics
          </h2>
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Overview Metrics */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Overview</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{analytics.overview.totalExecutions}</div>
              <div className="text-sm text-blue-800">Total Executions</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{getSuccessRate()}%</div>
              <div className="text-sm text-green-800">Success Rate</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">{formatDuration(analytics.overview.averageExecutionTime)}</div>
              <div className="text-sm text-purple-800">Avg Duration</div>
            </div>
            <div className="bg-orange-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-orange-600">{analytics.agentMetrics.totalConsultations}</div>
              <div className="text-sm text-orange-800">Agent Consultations</div>
            </div>
          </div>
        </div>

        {/* Template Usage */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <TrendingUpIcon className="h-5 w-5 mr-2" />
            Template Usage
          </h3>
          <div className="space-y-3">
            {analytics.templateUsage.map(template => (
              <div key={template.templateId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{template.templateName}</div>
                  <div className="text-sm text-gray-600">
                    {template.usageCount} executions • {Math.round(template.successRate * 100)}% success rate
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">{formatDuration(template.averageTime)}</div>
                  <div className="text-xs text-gray-600">avg time</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Execution Timeline */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ClockIcon className="h-5 w-5 mr-2" />
            Execution Timeline
          </h3>
          <div className="space-y-2">
            {analytics.timeSeriesData.map(day => (
              <div key={day.date} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-20 text-sm text-gray-600">{new Date(day.date).toLocaleDateString()}</div>
                <div className="flex-1 flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                    <CheckCircleIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-600">{day.successes}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <XCircleIcon className="h-4 w-4 text-red-600" />
                    <span className="text-sm text-red-600">{day.failures}</span>
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${(day.successes / day.executions) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-900">{day.executions} total</div>
              </div>
            ))}
          </div>
        </div>

        {/* Agent Consultation Metrics */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <UserGroupIcon className="h-5 w-5 mr-2" />
            Agent Consultation Metrics
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">By Agent</h4>
              <div className="space-y-2">
                {Object.entries(analytics.agentMetrics.consultationsByAgent).map(([agentId, count]) => (
                  <div key={agentId} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 capitalize">{agentId.replace('-', ' ')}</span>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">By Trigger Type</h4>
              <div className="space-y-2">
                {Object.entries(analytics.agentMetrics.consultationsByTrigger).map(([trigger, count]) => (
                  <div key={trigger} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 capitalize">{trigger.replace('_', ' ')}</span>
                    <span className="text-sm font-medium text-gray-900">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Performance Insights */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Peak Usage Hours</h4>
              <div className="text-sm text-gray-600">
                Most active: {analytics.performanceMetrics.peakHours
                  .sort((a, b) => b.count - a.count)
                  .slice(0, 3)
                  .map(h => `${h.hour}:00`)
                  .join(', ')}
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Common Issues</h4>
              <div className="space-y-1">
                {analytics.performanceMetrics.mostCommonFailureReasons.slice(0, 3).map(reason => (
                  <div key={reason.reason} className="text-sm text-gray-600">
                    {reason.reason}: {reason.count} occurrences
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
