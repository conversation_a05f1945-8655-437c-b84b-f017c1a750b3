'use client';

import React, { useState } from 'react';
import { BrainIcon, CalculatorIcon, ChartBarIcon } from '@heroicons/react/24/outline';

interface ComplexityAnalysis {
  totalScore: number;
  factors: {
    contentLength: { score: number; description: string };
    technicalTerms: { score: number; count: number; terms: string[] };
    targetAudience: { score: number; description: string };
    multipleGoals: { score: number; description: string };
    industryComplexity: { score: number; description: string };
  };
  recommendation: string;
  triggerThreshold: boolean;
}

interface ContentComplexityAnalyzerProps {
  className?: string;
}

export default function ContentComplexityAnalyzer({ className = '' }: ContentComplexityAnalyzerProps) {
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [industry, setIndustry] = useState('');
  const [goals, setGoals] = useState<string[]>(['']);
  const [analysis, setAnalysis] = useState<ComplexityAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzeComplexity = async () => {
    setIsAnalyzing(true);
    
    try {
      // Simulate the backend complexity calculation
      const context = {
        content,
        topic,
        targetAudience,
        industry,
        goals: goals.filter(g => g.trim())
      };

      // Calculate complexity using the same logic as the backend
      let complexity = 0;
      const factors: ComplexityAnalysis['factors'] = {
        contentLength: { score: 0, description: '' },
        technicalTerms: { score: 0, count: 0, terms: [] },
        targetAudience: { score: 0, description: '' },
        multipleGoals: { score: 0, description: '' },
        industryComplexity: { score: 0, description: '' }
      };

      // Content length factor
      if (content) {
        const contentLength = content.length;
        if (contentLength > 5000) {
          factors.contentLength.score = 0.4;
          factors.contentLength.description = 'Very long content (>5000 chars)';
        } else if (contentLength > 2000) {
          factors.contentLength.score = 0.3;
          factors.contentLength.description = 'Long content (2000-5000 chars)';
        } else if (contentLength > 1000) {
          factors.contentLength.score = 0.2;
          factors.contentLength.description = 'Medium content (1000-2000 chars)';
        } else if (contentLength > 500) {
          factors.contentLength.score = 0.1;
          factors.contentLength.description = 'Short content (500-1000 chars)';
        } else {
          factors.contentLength.description = 'Very short content (<500 chars)';
        }
        complexity += factors.contentLength.score;
      }

      // Technical terms factor
      const text = ((content || '') + ' ' + (topic || '')).toLowerCase();
      const technicalTerms = [
        'api', 'algorithm', 'framework', 'implementation', 'architecture',
        'database', 'authentication', 'encryption', 'microservices', 'kubernetes',
        'machine learning', 'artificial intelligence', 'blockchain', 'devops',
        'neural network', 'deep learning', 'cloud computing', 'containerization'
      ];
      const foundTerms = technicalTerms.filter(term => text.includes(term));
      factors.technicalTerms.count = foundTerms.length;
      factors.technicalTerms.terms = foundTerms;
      factors.technicalTerms.score = foundTerms.length * 0.05;
      factors.technicalTerms.description = `Found ${foundTerms.length} technical terms`;
      complexity += factors.technicalTerms.score;

      // Target audience complexity
      if (targetAudience) {
        const audienceLower = targetAudience.toLowerCase();
        let audienceScore = 0;
        let audienceDesc = 'General audience';
        
        if (audienceLower.includes('technical') || audienceLower.includes('expert')) {
          audienceScore += 0.2;
          audienceDesc = 'Technical/Expert audience';
        }
        if (audienceLower.includes('developer') || audienceLower.includes('engineer')) {
          audienceScore += 0.15;
          audienceDesc = 'Developer/Engineer audience';
        }
        if (audienceLower.includes('professional') || audienceLower.includes('specialist')) {
          audienceScore += 0.1;
          audienceDesc = 'Professional/Specialist audience';
        }
        
        factors.targetAudience.score = audienceScore;
        factors.targetAudience.description = audienceDesc;
        complexity += audienceScore;
      }

      // Multiple goals factor
      const validGoals = goals.filter(g => g.trim());
      if (validGoals.length > 3) {
        factors.multipleGoals.score = 0.1;
        factors.multipleGoals.description = `Multiple goals (${validGoals.length})`;
        complexity += 0.1;
      } else {
        factors.multipleGoals.description = `${validGoals.length} goals`;
      }

      // Industry complexity
      if (industry) {
        const complexIndustries = ['technology', 'healthcare', 'finance', 'legal', 'aerospace'];
        if (complexIndustries.some(ind => industry.toLowerCase().includes(ind))) {
          factors.industryComplexity.score = 0.1;
          factors.industryComplexity.description = 'Complex industry domain';
          complexity += 0.1;
        } else {
          factors.industryComplexity.description = 'Standard industry domain';
        }
      }

      const finalComplexity = Math.min(complexity, 1.0);
      
      let recommendation = '';
      if (finalComplexity > 0.7) {
        recommendation = 'High complexity content - recommend multiple agent consultations with technical specialists';
      } else if (finalComplexity > 0.4) {
        recommendation = 'Medium complexity content - recommend targeted agent consultation based on content type';
      } else {
        recommendation = 'Low complexity content - minimal agent consultation needed';
      }

      setAnalysis({
        totalScore: finalComplexity,
        factors,
        recommendation,
        triggerThreshold: finalComplexity > 0.6 // Default threshold for content_complexity trigger
      });

    } catch (error) {
      console.error('Failed to analyze complexity:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score > 0.7) return 'text-red-600';
    if (score > 0.4) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getScoreBackground = (score: number) => {
    if (score > 0.7) return 'bg-red-100';
    if (score > 0.4) return 'bg-yellow-100';
    return 'bg-green-100';
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <BrainIcon className="h-6 w-6 mr-2" />
          Content Complexity Analyzer
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Analyze content complexity to determine optimal agent consultation triggers
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Form */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Content Analysis Input</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Enter your content here..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Topic</label>
              <input
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="e.g., Machine Learning in Healthcare"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
              <input
                type="text"
                value={targetAudience}
                onChange={(e) => setTargetAudience(e.target.value)}
                placeholder="e.g., Technical professionals, Developers"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Industry</label>
              <input
                type="text"
                value={industry}
                onChange={(e) => setIndustry(e.target.value)}
                placeholder="e.g., Technology, Healthcare, Finance"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Goals</label>
              {goals.map((goal, index) => (
                <div key={index} className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    value={goal}
                    onChange={(e) => {
                      const newGoals = [...goals];
                      newGoals[index] = e.target.value;
                      setGoals(newGoals);
                    }}
                    placeholder={`Goal ${index + 1}`}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {index === goals.length - 1 && (
                    <button
                      onClick={() => setGoals([...goals, ''])}
                      className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      +
                    </button>
                  )}
                </div>
              ))}
            </div>

            <button
              onClick={analyzeComplexity}
              disabled={isAnalyzing}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Analyzing...</span>
                </>
              ) : (
                <>
                  <CalculatorIcon className="h-4 w-4" />
                  <span>Analyze Complexity</span>
                </>
              )}
            </button>
          </div>

          {/* Analysis Results */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Complexity Analysis</h3>
            
            {analysis ? (
              <div className="space-y-4">
                {/* Overall Score */}
                <div className={`p-4 rounded-lg ${getScoreBackground(analysis.totalScore)}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">Overall Complexity Score</span>
                    <span className={`text-2xl font-bold ${getScoreColor(analysis.totalScore)}`}>
                      {Math.round(analysis.totalScore * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        analysis.totalScore > 0.7 ? 'bg-red-600' :
                        analysis.totalScore > 0.4 ? 'bg-yellow-600' : 'bg-green-600'
                      }`}
                      style={{ width: `${analysis.totalScore * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-700 mt-2">{analysis.recommendation}</p>
                  
                  {analysis.triggerThreshold && (
                    <div className="mt-2 p-2 bg-blue-100 border border-blue-200 rounded text-sm text-blue-800">
                      ⚡ Would trigger content_complexity consultation (threshold: 60%)
                    </div>
                  )}
                </div>

                {/* Factor Breakdown */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900 flex items-center">
                    <ChartBarIcon className="h-4 w-4 mr-2" />
                    Complexity Factors
                  </h4>
                  
                  {Object.entries(analysis.factors).map(([key, factor]) => (
                    <div key={key} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                        </span>
                        <span className="text-sm font-bold text-gray-700">
                          +{Math.round(factor.score * 100)}%
                        </span>
                      </div>
                      <div className="text-xs text-gray-600">{factor.description}</div>
                      
                      {key === 'technicalTerms' && factor.terms && factor.terms.length > 0 && (
                        <div className="mt-2">
                          <div className="text-xs text-gray-500 mb-1">Found terms:</div>
                          <div className="flex flex-wrap gap-1">
                            {factor.terms.slice(0, 5).map(term => (
                              <span key={term} className="px-1 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">
                                {term}
                              </span>
                            ))}
                            {factor.terms.length > 5 && (
                              <span className="text-xs text-gray-500">+{factor.terms.length - 5} more</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BrainIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>Enter content details and click "Analyze Complexity"</p>
                <p className="text-sm mt-1">Get insights into content complexity scoring</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
